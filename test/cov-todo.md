- [x] `Drops.Relation.Application` - 100.0%
  - [x] `start/2`

- [x] `Drops.Relation.Compilers.SqliteSchemaCompiler` - 100.0%
  - [x] `visit/3`

- [x] `Drops.Relation.Compilers.PostgresSchemaCompiler` - 100.0%
  - [x] `visit/3`
  - [x] `visit/4`

- [x] `Drops.Relation.Compilers.EctoCompiler` - 92.0%
  - [x] `visit/2`

- [x] `Drops.Relation` - 91.7%
  - [x] `__define_relation__/2`
  - [ ] `new/3`
  - [ ] `__schema__/1`
  - [ ] `__schema__/2`
  - [ ] `schema/0`
  - [ ] `restrict/1`
  - [ ] `restrict/2`
  - [ ] `ecto_schema/1`
  - [ ] `ecto_schema/2`
  - [ ] `association/1`
  - [ ] `associations/0`
  - [ ] `struct/1`
  - [ ] `preload/1`
  - [ ] `preload/2`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [ ] `reduce/3`
  - [ ] `to_query/1`

- [x] `Drops.SQL.Compilers.Sqlite` - 89.3%
  - [x] `visit/3`

- [x] `Drops.SQL.Compilers.Postgres` - 86.7%
  - [x] `visit/3`

- [x] `Drops.Relation.Cache` - 86.0%
  - [x] `maybe_get_cached_schema/2`
  - [x] `get_cached_schema/2`
  - [ ] `get_or_infer/2`
  - [x] `cache_schema/3`
  - [x] `clear_repo_cache/1`
  - [x] `clear_all/0`
  - [x] `warm_up/2`
  - [x] `refresh/2`
  - [x] `get_cache_file_path/2`

- [x] `Drops.Relation.Compilers.CodeCompiler` - 82.6%
  - [x] `visit/2`
  - [x] `visit/3`
  - [x] `visit/5`
  - [x] `visit/4`
  - [ ] `visit/0`
  - [x] `visit/6`

- [x] `Drops.SQL.Postgres` - 81.8%
  - [x] `introspect_table/2`

- [ ] `Drops.SQL.Sqlite` - 79.6%
  - [x] `introspect_table/2`

- [ ] `Drops.Relation.Schema.Serializable` - 78.3%
  - [ ] `encode/2`
  - [x] `name/0`
  - [x] `load/1`
  - [x] `load/2`
  - [x] `dump/1`
  - [x] `dump/2`
  - [x] `load/4`

- [ ] `Drops.SQL.Database` - 70.0%
  - [ ] `introspect_table/2`
  - [ ] `opts/0`
  - [ ] `adapter/0`
  - [ ] `table/2`
  - [x] `compile_table/3`

- [ ] `Drops.Relation.Schema.Patcher` - 68.9%
  - [x] `patch_schema_module/3`
  - [x] `update_attributes/2`
  - [x] `update_schema_block/3`

- [ ] `Drops.SQL.Database.Table` - 65.2%
  - [x] `fetch/2`
  - [x] `get_and_update/3`
  - [x] `pop/2`
  - [x] `new/6`
  - [x] `from_introspection/5`
  - [ ] `get_column/2`
  - [ ] `column_names/1`
  - [ ] `primary_key_column_names/1`
  - [ ] `foreign_key_column_names/1`
  - [ ] `primary_key_column?/2`
  - [ ] `foreign_key_column?/2`
  - [ ] `get_foreign_key_for_column/2`

- [ ] `Drops.Relation.Compilers.SchemaCompiler` - 60.0%
  - [ ] `visit/2`
  - [ ] `opts/0`
  - [ ] `process/2`
  - [ ] `visit/3`

- [ ] `Drops.Relation.Generator` - 57.6%
  - [x] `generate_schema_module/3`
  - [x] `generate_module_content/3`
  - [ ] `schema_module/3`
  - [ ] `generate_schema_module_string/3`
  - [ ] `generate_schema_module_body/3`
  - [ ] `generate_module_body_content/2`
  - [x] `sync_schema_content/3`
  - [x] `extract_module_name/1`
  - [ ] `generate_schema_parts/2`
  - [x] `generate_schema_ast_from_schema/1`
  - [x] `schema_from_block/2`
  - [ ] `update_schema_with_zipper/3`

- [ ] `Drops.Relation.Schema.Field` - 55.2%
  - [x] `new/3`
  - [ ] `new/5`
  - [x] `merge/2`
  - [ ] `same_name?/2`
  - [x] `matches_name?/2`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [x] `reduce/3`

- [ ] `Drops.Relation.Schema` - 54.7%
  - [x] `new/5`
  - [x] `new/1`
  - [x] `empty/1`
  - [x] `merge/2`
  - [x] `find_field/2`
  - [ ] `primary_key_field?/2`
  - [ ] `foreign_key_field?/2`
  - [ ] `get_foreign_key/2`
  - [x] `composite_primary_key?/1`
  - [ ] `field_names/1`
  - [ ] `foreign_key_field_names/1`
  - [ ] `source_table/1`
  - [x] `fetch/2`
  - [ ] `get_and_update/3`
  - [ ] `pop/2`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [x] `reduce/3`

- [ ] `Drops.Relation.Schema.PrimaryKey` - 54.2%
  - [x] `new/1`
  - [x] `composite?/1`
  - [ ] `present?/1`
  - [x] `field_names/1`
  - [x] `merge/2`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [x] `reduce/3`

- [ ] `Drops.SQL.Database.PrimaryKey` - 44.4%
  - [x] `new/1`
  - [x] `from_columns/1`
  - [ ] `composite?/1`
  - [ ] `present?/1`
  - [ ] `column_names/1`
  - [ ] `includes_column?/2`
  - [ ] `column_count/1`

- [ ] `Drops.SQL.Database.Column` - 40.0%
  - [x] `new/3`
  - [x] `primary_key?/1`
  - [ ] `nullable?/1`
  - [ ] `has_default?/1`
  - [ ] `has_check_constraints?/1`

- [ ] `Drops.Relation.Query` - 39.8%
  - [x] `generate_functions/2`
  - [x] `get/3`
  - [ ] `get!/3`
  - [x] `get_by/3`
  - [ ] `get_by!/3`
  - [x] `all/2`
  - [ ] `one/2`
  - [ ] `one!/2`
  - [x] `insert/2`
  - [ ] `insert!/2`
  - [x] `update/2`
  - [ ] `update!/2`
  - [x] `delete/2`
  - [ ] `delete!/2`
  - [x] `count/3`
  - [ ] `first/2`
  - [ ] `last/2`
  - [ ] `get_by_field/3`
  - [ ] `get/2`
  - [ ] `get!/2`
  - [ ] `get_by/2`
  - [ ] `get_by!/2`
  - [ ] `count/2`
  - [ ] `unquote/1`

- [ ] `Drops.Relation.Config` - 25.0%
  - [x] `validate!/0`
  - [x] `validate!/1`
  - [x] `persist/1`
  - [ ] `schema_cache/0`
  - [ ] `put_config/2`
  - [ ] `update/2`

- [ ] `Drops.Relation.Schema.Index` - 20.0%
  - [x] `new/4`
  - [x] `composite?/1`
  - [x] `field_names/1`
  - [ ] `covers_field?/2`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [ ] `reduce/3`

- [ ] `Drops.Relation.Schema.Indices` - 18.2%
  - [x] `new/1`
  - [ ] `add_index/2`
  - [ ] `find_by_field/2`
  - [ ] `unique_indices/1`
  - [ ] `composite_indices/1`
  - [x] `empty?/1`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [ ] `reduce/3`

- [ ] `Drops.SQL.Database.ForeignKey` - 16.7%
  - [x] `new/0`
  - [ ] `composite?/1`
  - [ ] `column_names/1`
  - [ ] `referenced_column_names/1`
  - [ ] `includes_column?/2`
  - [ ] `column_count/1`

- [ ] `Drops.SQL.Database.Index` - 14.3%
  - [x] `new/3`
  - [ ] `composite?/1`
  - [ ] `unique?/1`
  - [ ] `partial?/1`
  - [ ] `column_names/1`
  - [ ] `includes_column?/2`
  - [ ] `column_count/1`

- [ ] `Drops.Relation.Schema.ForeignKey` - 11.1%
  - [x] `new/3`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [ ] `reduce/3`

- [ ] `Mix.Tasks.Drops.Relation.RefreshCache` - 0.0%
  - [ ] `run/1`

- [ ] `Drops.SQL.Compiler` - 0.0%
  - [ ] `visit/3`
  - [ ] `opts/0`
  - [ ] `process/2`
  - [ ] `visit/2`

- [ ] `Drops.Relation.Composite` - 0.0%
  - [ ] `new/4`
  - [ ] `infer_association/2`
  - [ ] `to_query/1`
  - [ ] `count/1`
  - [ ] `member?/2`
  - [ ] `slice/1`
  - [ ] `reduce/3`

- [ ] `Mix.Tasks.Drops.Relation.GenSchemas` - 0.0%
  - [ ] `info/2`
  - [ ] `igniter/1`

- [ ] `Drops.SQL.Types.Sqlite` - 0.0%
  - [ ] `to_ecto_type/2`
  - [ ] `to_ecto_type/1`

- [ ] `Drops.SQL.Types.Postgres` - 0.0%
  - [ ] `to_ecto_type/2`
  - [ ] `to_ecto_type/1`

- [ ] `Mix.Tasks.Drops.Relation.DevSetup` - 0.0%
  - [ ] `run/1`
